<!DOCTYPE html>
<html>

<head>
  <style>
    body {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    table {
      width:750px;
      border-bottom: red;
      border-spacing: 30px 30px;
      padding: 0 100px 100px 0;
      border-collapse: collapse;
    }

    th,
    td {
      padding: 20px;
      text-align: left;
      border-bottom: 2px solid #ddd;
    }

    .titleBar {
      display: flex;
      flex-direction: column;
      background-color: #007DB8;
      position: relative;
      height:80px;
      width:800px;
      margin-top: 20px;
      text-align: center;
    }

    .title {
      display: flex;
      justify-content: center;
    }
    .title > h2 {
      margin: 0px 0px 4px 0px;
      color: #ffffff;
      text-align: center;
    }

    img {
      width: 40px;
      height: 40px;
      margin: 4px 0px 0px 4px;
      left: 0;
    }
  </style>
</head>

<body lang="EN-US" link="blue" vlink="purple" style="tab-interval:.5in">

<p class="MsoNormal"><o:p>&nbsp;</o:p></p>

<p class="MsoNormal"><o:p>&nbsp;</o:p></p>

<div align="center">

<table class="MsoNormalTable" border="0" cellspacing="0" cellpadding="0" width="60%" style="width:60.0%;mso-cellspacing:0in;mso-yfti-tbllook:1184;mso-padding-alt:
 0in 0in 0in 0in">
 <tbody><tr style="mso-yfti-irow:0;mso-yfti-firstrow:yes">
  <td style="padding:0in 0in 0in 0in">
  <p class="MsoNormal"><!-- [if gte vml 1]><v:shapetype id="_x0000_t75"
   coordsize="21600,21600" o:spt="75" o:preferrelative="t" path="m@4@5l@4@11@9@11@9@5xe"
   filled="f" stroked="f">
   <v:stroke joinstyle="miter"/>
   <v:formulas>
    <v:f eqn="if lineDrawn pixelLineWidth 0"/>
    <v:f eqn="sum @0 1 0"/>
    <v:f eqn="sum 0 0 @1"/>
    <v:f eqn="prod @2 1 2"/>
    <v:f eqn="prod @3 21600 pixelWidth"/>
    <v:f eqn="prod @3 21600 pixelHeight"/>
    <v:f eqn="sum @0 0 1"/>
    <v:f eqn="prod @6 1 2"/>
    <v:f eqn="prod @7 21600 pixelWidth"/>
    <v:f eqn="sum @8 21600 0"/>
    <v:f eqn="prod @7 21600 pixelHeight"/>
    <v:f eqn="sum @10 21600 0"/>
   </v:formulas>
   <v:path o:extrusionok="f" gradientshapeok="t" o:connecttype="rect"/>
   <o:lock v:ext="edit" aspectratio="t"/>
  </v:shapetype><v:shape id="_x0000_i1025" type="#_x0000_t75" alt="" style='width:537.5pt;
   height:64pt'>
   <v:imagedata src="FW%20Your%20API%20has%20been%20registered%20in%20Development%20marketplace%20-%20eis-activity-monitoring_files/image001.png"
    o:href="cid:image001.png@01D44115.9E4E0960"/>
  </v:shape><![endif]--><!--[if !vml]-->  </td>
 </tr>
 <tr style="mso-yfti-irow:1">
  <td style="padding:0in 0in 0in 21.75pt">
  <p><br>
  <b>Hello,</b><br>
  <br>
  Your API has been successfully registered in ${environment} marketplace. <br>
  <br>
  Please find the API details as below,<br>
  <br>
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>API Name: </b>${apiName}
  <br>
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>Authentication Method: </b>API Key <br>
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>Basic Auth Users: </b><br>
  
  <p class="MsoNormal">Feel free to reach out to <a href="mailto:<EMAIL>"><EMAIL></a>
  DL for any questions on API marketplace. </p>
  </td>
 </tr>
 <tr style="mso-yfti-irow:2;mso-yfti-lastrow:yes">
  <td style="padding:0in 0in 0in 21.75pt">
  <p><br>
  <b>Thank You,</b> </p>
  <p><b>API Cloud Platform Team</b> </p>
  </td>
 </tr>
</tbody></table>

</div>

<p class="MsoNormal"><o:p>&nbsp;</o:p></p>

</div>




</body>
</html>
