<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.emc.it.eis</groupId>
    <artifactId>cic-rest-parent</artifactId>
    <version>6.1.0.RELEASE</version>
  </parent>
  <groupId>com.dell.aic.email</groupId>
  <artifactId>email-service</artifactId>
  <version>${PROJECT_VERSION}</version>
  <packaging>war</packaging>
  <name>email-service</name>
  <description>Generic-Email-API</description>
  <dependencies>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.ws</groupId>
      <artifactId>jaxws-rt</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.emc.it.eis</groupId>
      <artifactId>oauth2-server</artifactId>
    </dependency>
    
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-velocity</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.apache.velocity</groupId>
          <artifactId>velocity</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.struts</groupId>
          <artifactId>struts-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>dom4j</groupId>
          <artifactId>dom4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>jboss-logging</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.tika</groupId>
      <artifactId>tika-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.mail</groupId>
      <artifactId>jakarta.mail-api</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.emc.it.eis</groupId>
      <artifactId>oauth2-rest-client</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
