package com.dell.it.eis.email.controller;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ExecutionException;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.ApiResponse;
import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.exception.BadRequestException;
import com.dell.it.eis.email.exception.CannotSendEmailException;
import com.dell.it.eis.email.service.EmailService;
import com.dell.it.eis.email.util.EmailUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

@RestController
@RefreshScope
@Slf4j
public class EmailController {

	@Autowired
	EmailService emailService;

	@Autowired
	EmailUtil emailUtil;

	private static final String SUCCESS_STATUS = "Mail sent successfully";

	private SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:MM:SS");

	/**
	 * The below API will be used to send a mail based on the template name and
	 * template value provided in the request
	 * 
	 * Here we need to pass reqjson as a file attachement
	 * 
	 * @param genericEmailDto
	 * @param templateName
	 * @return ResponseEntity<ApiResponse>
	 * @throws Exception
	 * @throws MessagingException
	 * @throws AddressException
	 * 
	 */
	
	
	@GetMapping(value = "/OAuthTesting")
	public void getEmail() {
		log.info("Hello Mail");
	}
	
	@PostMapping(value = "/api/v1/generic/email", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<ApiResponse> sendGenericEmail(@RequestPart("reqjson") GenericEmailDto emailControllerGenericEmailDto,
			@RequestPart(name = "file", required = false) MultipartFile emailControllerFile,
			@RequestParam(name = "template", required = false) String emailControllerTemplateName)
			throws AddressException, MessagingException {

		log.info("Going to trigger the email for email address {}", emailControllerGenericEmailDto.getToAddress());
		ResponseEntity<ApiResponse> emailControllerResponse = null;
		//Validates the request
		emailUtil.validateRequest(emailControllerGenericEmailDto, emailControllerTemplateName);
		try {
			emailService.sendGenericEmail(emailControllerGenericEmailDto, emailControllerTemplateName, emailControllerFile);
			log.info("Email sent successfully");
			emailControllerResponse = new ResponseEntity<>(new ApiResponse(dateFormat.format(new Date()), SUCCESS_STATUS,
					"to " + emailControllerGenericEmailDto.getToAddress()), HttpStatus.OK);
		} catch (CannotSendEmailException excep) {
			excep.printStackTrace();
			log.error("Error while sending mail {}", excep.getMessage());
			throw new CannotSendEmailException(excep.getMessage(), excep);
		}
		return emailControllerResponse;
	}

	/**
	 * The below API will be used to send a mail based on the template name and
	 * template value provided in the request
	 * 
	 * Here we need to pass reqjson as a String JSON, which will be internally
	 * converted to DTO using mapper
	 * 
	 * @param genericEmailDto
	 * @param templateName
	 * @return ResponseEntity<ApiResponse>
	 * @throws Exception
	 * @throws MessagingException
	 * @throws AddressException
	 */

	@PostMapping(value = "/api/v2/generic/email", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<ApiResponse> sendGenericEmailAsyn(@RequestPart("reqjson") String emailControllerGenericEmailDtoString,
			@RequestPart(name = "file", required = false) MultipartFile emailControllerMultipartfile,
			@RequestParam(name = "template", required = false) String emailControllerTemplateNameString, HttpServletRequest request)
			throws AddressException, MessagingException {

		log.info("Going to trigger the email for email address {}");
		ResponseEntity<ApiResponse> response = null;
		GenericEmailDto genericEmailDto = null;
		try {
			genericEmailDto = new ObjectMapper().readValue(emailControllerGenericEmailDtoString, GenericEmailDto.class);
		} catch (Exception ex) {
			log.error("Error while converting string json to DTO. Json Passed is {}", emailControllerGenericEmailDtoString);
			throw new BadRequestException(
					"Error while converting string json to DTO. Please check the json fields properly.");

		}
		//Validates the request
		emailUtil.validateRequest(genericEmailDto, emailControllerTemplateNameString);
		try {			
			emailService.sendGenericEmail(genericEmailDto, emailControllerTemplateNameString, emailControllerMultipartfile);
			log.info("Email Request submitted successfully");
			response = new ResponseEntity<>(new ApiResponse(dateFormat.format(new Date()), SUCCESS_STATUS,
					"to " + genericEmailDto.getToAddress()), HttpStatus.OK);
		} catch (CannotSendEmailException exception) {
			exception.printStackTrace();
			log.error("Error while sending mail {}", exception.getMessage());
			throw new CannotSendEmailException(exception.getMessage(), exception);
		}
		return response;
	}

}
