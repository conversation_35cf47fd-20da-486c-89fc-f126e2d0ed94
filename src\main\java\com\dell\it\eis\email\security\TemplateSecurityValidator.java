package com.dell.it.eis.email.security;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.dell.it.eis.email.config.TemplateSecurityProperties;
import com.dell.it.eis.email.exception.TemplateSecurityException;

import lombok.extern.slf4j.Slf4j;

/**
 * Security validator for Velocity templates to prevent code injection attacks
 */
@Component
@Slf4j
public class TemplateSecurityValidator {

    @Autowired
    private TemplateSecurityProperties securityProperties;

    // Dangerous VTL patterns that could lead to code injection
    private static final List<Pattern> DANGEROUS_PATTERNS = Arrays.asList(
        // Class instantiation and method calls
        Pattern.compile("\\$\\{?[a-zA-Z_][a-zA-Z0-9_]*\\.class\\}?", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\$\\{?[a-zA-Z_][a-zA-Z0-9_]*\\.getClass\\(\\)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\$\\{?\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*\\.\\s*forName\\s*\\(", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\$\\{?\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*\\.\\s*newInstance\\s*\\(", Pattern.CASE_INSENSITIVE),
        
        // Runtime and system access
        Pattern.compile("Runtime\\s*\\.\\s*getRuntime", Pattern.CASE_INSENSITIVE),
        Pattern.compile("System\\s*\\.\\s*(exit|getProperty|setProperty|getenv)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("ProcessBuilder", Pattern.CASE_INSENSITIVE),
        
        // File system access
        Pattern.compile("java\\.io\\.(File|FileInputStream|FileOutputStream|FileReader|FileWriter)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("java\\.nio\\.file", Pattern.CASE_INSENSITIVE),
        
        // Network access
        Pattern.compile("java\\.net\\.(URL|URLConnection|Socket|ServerSocket)", Pattern.CASE_INSENSITIVE),
        
        // Reflection
        Pattern.compile("java\\.lang\\.reflect", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.getDeclaredMethod", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.getMethod", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.invoke", Pattern.CASE_INSENSITIVE),
        
        // Script execution
        Pattern.compile("javax\\.script", Pattern.CASE_INSENSITIVE),
        Pattern.compile("ScriptEngine", Pattern.CASE_INSENSITIVE),
        
        // Dangerous Velocity directives
        Pattern.compile("#evaluate\\s*\\(", Pattern.CASE_INSENSITIVE),
        Pattern.compile("#include\\s*\\(", Pattern.CASE_INSENSITIVE),
        Pattern.compile("#parse\\s*\\(", Pattern.CASE_INSENSITIVE),
        
        // Class loader access
        Pattern.compile("\\.getClassLoader", Pattern.CASE_INSENSITIVE),
        Pattern.compile("ClassLoader", Pattern.CASE_INSENSITIVE)
    );

    /**
     * Validates a Velocity template for security issues
     * 
     * @param template The template content to validate
     * @throws TemplateSecurityException if the template contains security risks
     */
    public void validateTemplate(String template) {
        if (!StringUtils.hasText(template)) {
            return; // Empty templates are safe
        }

        log.debug("Validating template security for template of length: {}", template.length());

        int maxSize = securityProperties.getMaxSizeSafe();

        // Check template size
        if (template.length() > maxSize) {
            if (securityProperties.getLogSecurityViolations()) {
                log.warn("Template size exceeds maximum allowed size: {} > {}", template.length(), maxSize);
            }
            throw new TemplateSecurityException("Template size exceeds maximum allowed size of " + maxSize + " characters");
        }

        // Check for dangerous patterns
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            if (pattern.matcher(template).find()) {
                if (securityProperties.getLogSecurityViolations()) {
                    log.warn("Dangerous pattern detected in template: {}", pattern.pattern());
                }
                if (securityProperties.getStrictValidation()) {
                    throw new TemplateSecurityException("Template contains potentially dangerous content: " + pattern.pattern());
                } else {
                    log.info("Dangerous pattern detected but strict validation is disabled: {}", pattern.pattern());
                }
            }
        }

        // Check nesting depth
        validateNestingDepth(template);

        log.debug("Template security validation passed");
    }

    /**
     * Validates that Velocity constructs don't exceed maximum nesting depth
     */
    private void validateNestingDepth(String template) {
        int maxDepth = 0;
        int currentDepth = 0;
        
        // Simple depth tracking for common Velocity constructs
        String[] lines = template.split("\n");
        for (String line : lines) {
            String trimmedLine = line.trim();
            
            // Count opening constructs
            if (trimmedLine.matches(".*#(if|foreach|while|macro)\\s*\\(.*")) {
                currentDepth++;
                maxDepth = Math.max(maxDepth, currentDepth);
            }
            
            // Count closing constructs
            if (trimmedLine.matches(".*#(end|else).*")) {
                currentDepth = Math.max(0, currentDepth - 1);
            }
        }

        int maxNestingDepth = securityProperties.getMaxNestingDepthSafe();

        if (maxDepth > maxNestingDepth) {
            if (securityProperties.getLogSecurityViolations()) {
                log.warn("Template nesting depth exceeds maximum: {} > {}", maxDepth, maxNestingDepth);
            }
            if (securityProperties.getStrictValidation()) {
                throw new TemplateSecurityException("Template nesting depth exceeds maximum allowed depth of " + maxNestingDepth);
            } else {
                log.info("Template nesting depth exceeds maximum but strict validation is disabled: {} > {}", maxDepth, maxNestingDepth);
            }
        }
    }

    /**
     * Sanitizes template content by removing or escaping potentially dangerous content
     * This is a fallback method - validation should be the primary security measure
     * 
     * @param template The template to sanitize
     * @return Sanitized template content
     */
    public String sanitizeTemplate(String template) {
        if (!StringUtils.hasText(template)) {
            return template;
        }

        String sanitized = template;
        
        // Remove dangerous directives
        sanitized = sanitized.replaceAll("(?i)#evaluate\\s*\\([^)]*\\)", "## REMOVED: evaluate directive");
        sanitized = sanitized.replaceAll("(?i)#include\\s*\\([^)]*\\)", "## REMOVED: include directive");
        sanitized = sanitized.replaceAll("(?i)#parse\\s*\\([^)]*\\)", "## REMOVED: parse directive");
        
        // Escape class access patterns
        sanitized = sanitized.replaceAll("(?i)\\.class\\b", ".CLASS_REMOVED");
        sanitized = sanitized.replaceAll("(?i)\\.getClass\\s*\\(", ".getClass_REMOVED(");
        
        log.debug("Template sanitization completed");
        return sanitized;
    }
}
