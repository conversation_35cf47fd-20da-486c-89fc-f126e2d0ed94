package com.dell.it.eis.email.security;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import com.dell.it.eis.email.config.TemplateSecurityProperties;
import com.dell.it.eis.email.exception.TemplateSecurityException;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SecureTemplateServiceTest {

    @Mock
    private TemplateSecurityValidator templateValidator;

    @Mock
    private VelocityEngine velocityEngine;

    @Mock
    private TemplateSecurityProperties securityProperties;

    @InjectMocks
    private SecureTemplateService secureTemplateService;

    private Map<String, Object> testModel;

    @BeforeEach
    void setUp() {
        testModel = new HashMap<>();
        testModel.put("name", "John Doe");
        testModel.put("orderNumber", "12345");

        // Set up default mock behavior for security properties
        when(securityProperties.getProcessingTimeoutSafe()).thenReturn(5000L);
        when(securityProperties.getMaxOutputSizeSafe()).thenReturn(500000);
        when(securityProperties.getLogSecurityViolations()).thenReturn(true);
    }

    @Test
    void testProcessTemplateSecurely_ValidTemplate_ShouldSucceed() {
        String template = "Hello $name, your order #$orderNumber is ready!";
        String expectedOutput = "Hello John Doe, your order #12345 is ready!";

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine to return success
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                // Simulate writing to the StringWriter
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write(expectedOutput);
                return true;
            });

        String result = secureTemplateService.processTemplateSecurely(template, testModel);

        assertEquals(expectedOutput, result);
        verify(templateValidator).validateTemplate(template);
        verify(velocityEngine).evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template));
    }

    @Test
    void testProcessTemplateSecurely_ValidationFails_ShouldThrowException() {
        String maliciousTemplate = "Hello ${Runtime.getRuntime()}";

        // Mock validator to fail
        doThrow(new TemplateSecurityException("Dangerous content detected"))
            .when(templateValidator).validateTemplate(maliciousTemplate);

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(maliciousTemplate, testModel)
        );

        assertEquals("Dangerous content detected", exception.getMessage());
        verify(templateValidator).validateTemplate(maliciousTemplate);
        verifyNoInteractions(velocityEngine);
    }

    @Test
    void testProcessTemplateSecurely_VelocityProcessingFails_ShouldThrowException() {
        String template = "Hello $name";

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine to return failure
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenReturn(false);

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(template, testModel)
        );

        assertTrue(exception.getMessage().contains("Template processing failed"));
        verify(templateValidator).validateTemplate(template);
        verify(velocityEngine).evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template));
    }

    @Test
    void testProcessTemplateSecurely_ExcessiveOutputSize_ShouldThrowException() {
        String template = "Hello $name";
        StringBuilder largeOutput = new StringBuilder();
        for (int i = 0; i < 600000; i++) {
            largeOutput.append("a");
        }

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine to return large output
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write(largeOutput.toString());
                return true;
            });

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(template, testModel)
        );

        assertTrue(exception.getMessage().contains("output exceeds maximum size"));
        verify(securityProperties).getMaxOutputSizeSafe();
        verify(securityProperties).getLogSecurityViolations();
    }

    @Test
    void testProcessTemplateSecurely_ConfigurableOutputSize_ShouldUseCustomLimit() {
        String template = "Hello $name";

        // Set custom output size limit
        when(securityProperties.getMaxOutputSizeSafe()).thenReturn(5);

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);

        // Mock velocity engine to return output larger than custom limit
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write("Hello John Doe"); // 14 characters > 5
                return true;
            });

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(template, testModel)
        );

        assertTrue(exception.getMessage().contains("output exceeds maximum size"));
        verify(securityProperties).getMaxOutputSizeSafe();
    }

    @Test
    void testProcessTemplateSecurely_ConfigurableTimeout_ShouldUseCustomLimit() {
        String template = "Hello $name";

        // Set very short timeout
        when(securityProperties.getProcessingTimeoutSafe()).thenReturn(1L);

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);

        // Mock velocity engine to simulate slow processing
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                try {
                    Thread.sleep(10); // Sleep longer than timeout
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write("Hello John");
                return true;
            });

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(template, testModel)
        );

        assertTrue(exception.getMessage().contains("timeout"));
        verify(securityProperties).getProcessingTimeoutSafe();
    }

    @Test
    void testProcessTemplateSecurely_WithInvalidContextKeys_ShouldFilterThem() {
        String template = "Hello $name";
        Map<String, Object> modelWithInvalidKeys = new HashMap<>();
        modelWithInvalidKeys.put("name", "John");
        modelWithInvalidKeys.put("invalid-key", "value");
        modelWithInvalidKeys.put("123invalid", "value");
        modelWithInvalidKeys.put("", "value");
        modelWithInvalidKeys.put(null, "value");

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                VelocityContext context = (VelocityContext) invocation.getArgument(0);
                // Verify only valid keys are in context
                assertTrue(context.containsKey("name"));
                assertFalse(context.containsKey("invalid-key"));
                assertFalse(context.containsKey("123invalid"));
                
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write("Hello John");
                return true;
            });

        String result = secureTemplateService.processTemplateSecurely(template, modelWithInvalidKeys);

        assertEquals("Hello John", result);
    }

    @Test
    void testProcessTemplateSecurely_WithDangerousContextValues_ShouldFilterThem() {
        String template = "Hello $name";
        Map<String, Object> modelWithDangerousValues = new HashMap<>();
        modelWithDangerousValues.put("name", "John");
        modelWithDangerousValues.put("runtime", Runtime.getRuntime());
        modelWithDangerousValues.put("classObj", String.class);

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                VelocityContext context = (VelocityContext) invocation.getArgument(0);
                // Verify only safe values are in context
                assertTrue(context.containsKey("name"));
                assertFalse(context.containsKey("runtime"));
                assertFalse(context.containsKey("classObj"));
                
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write("Hello John");
                return true;
            });

        String result = secureTemplateService.processTemplateSecurely(template, modelWithDangerousValues);

        assertEquals("Hello John", result);
    }

    @Test
    void testProcessTemplateSecurely_WithNullModel_ShouldWork() {
        String template = "Hello World";

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenAnswer(invocation -> {
                java.io.StringWriter writer = (java.io.StringWriter) invocation.getArgument(1);
                writer.write("Hello World");
                return true;
            });

        String result = secureTemplateService.processTemplateSecurely(template, null);

        assertEquals("Hello World", result);
    }

    @Test
    void testProcessTemplateSecurely_UnexpectedException_ShouldWrapException() {
        String template = "Hello $name";

        // Mock validator to pass
        doNothing().when(templateValidator).validateTemplate(template);
        
        // Mock velocity engine to throw unexpected exception
        when(velocityEngine.evaluate(any(VelocityContext.class), any(), eq("SecureTemplate"), eq(template)))
            .thenThrow(new RuntimeException("Unexpected error"));

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(template, testModel)
        );

        assertTrue(exception.getMessage().contains("unexpected error"));
        assertNotNull(exception.getCause());
    }
}
