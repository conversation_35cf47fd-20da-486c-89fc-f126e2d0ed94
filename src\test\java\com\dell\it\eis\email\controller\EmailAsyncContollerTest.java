package com.dell.it.eis.email.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import com.dell.it.eis.email.domain.ApiResponse;
import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.exception.CannotSendEmailException;
import com.dell.it.eis.email.service.EmailService;
import com.dell.it.eis.email.util.EmailUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.servlet.http.HttpServletRequest;

class EmailAsyncContollerTest {

	EmailAsyncContoller emailAsyncContoller;
	String emailAyncControllerJson;
	EmailService emailService;
	EmailUtil emailUtil;
	GenericEmailDto emailAsyncControllerGenericEmailDto = new GenericEmailDto();
	private final String toAddress = "<EMAIL>";
	HttpServletRequest request;
	GenericEmailDto emailControllerGenericEmailDto = new GenericEmailDto();
	private String emailControllerGenericEmailDtoString;


	@BeforeEach
	void setUp() {
		emailAsyncContoller = new EmailAsyncContoller();
		emailService = Mockito.mock(EmailService.class);
		emailUtil = Mockito.mock(EmailUtil.class);
		emailAsyncControllerGenericEmailDto.setToAddress(toAddress);
		emailAyncControllerJson = new Gson().toJson(emailAsyncControllerGenericEmailDto);

		ReflectionTestUtils.setField(emailAsyncContoller, "emailService", emailService);
		ReflectionTestUtils.setField(emailAsyncContoller, "emailUtil", emailUtil);
	}

	@Test
	void send_generic_email_asyn() throws AddressException, InterruptedException, ExecutionException, MessagingException, IOException {
		doNothing().when(emailService).sendGenericEmailAsync(Mockito.any(), Mockito.any(), Mockito.any());
		doNothing().when(emailUtil).validateRequest(any(), any());
		ResponseEntity<ApiResponse> response = emailAsyncContoller.sendGenericEmailAsyn(emailAsyncControllerGenericEmailDto, Mockito.any(), Mockito.any(), request);
		Assertions.assertNotNull(response);
	}

	@Test
	void send_generic_email_asyn_test() throws AddressException, MessagingException, IOException {
		doNothing().when(emailService).sendGenericEmail(Mockito.any(), Mockito.any(), Mockito.any());
		doNothing().when(emailUtil).validateRequest(any(), any());
		ResponseEntity<ApiResponse> response = emailAsyncContoller.sendGenericEmailAsyn(emailAyncControllerJson, Mockito.any(),
				Mockito.any(), request);
		Assertions.assertNotNull(response);
	}

	@Test
	void testsendGenericEmailAsynGivenException() throws AddressException, MessagingException, IOException {

		String emailControllerTemplateName = null;
		doNothing().when(emailUtil).validateRequest(emailControllerGenericEmailDto, emailControllerTemplateName);
		doThrow(new CannotSendEmailException()).when(emailService).sendEmailAsync(Mockito.any(), Mockito.any(), Mockito.any());
		try {
			emailAsyncContoller.sendGenericEmailAsyn(emailControllerGenericEmailDto, null, emailControllerTemplateName, request);
		} catch (Exception e) { }
	}

	@Test
	void testsendGenericEmailAsynGivenExceptionWithString() throws AddressException, MessagingException, IOException {

		String emailControllerTemplateName = null;
		doNothing().when(emailUtil).validateRequest(emailControllerGenericEmailDto, emailControllerTemplateName);
		doThrow(new CannotSendEmailException()).when(emailService).sendEmailAsync(Mockito.any(), Mockito.any(), Mockito.any());
		try {
			emailAsyncContoller.sendGenericEmailAsyn(emailControllerGenericEmailDtoString, null, emailControllerTemplateName, request);
		} catch (Exception e) { }

		GenericEmailDto dto = new GenericEmailDto();
		dto.setFromAddress("test");
		emailControllerGenericEmailDtoString = new ObjectMapper().writeValueAsString(dto);
		try {
			emailAsyncContoller.sendGenericEmailAsyn(emailControllerGenericEmailDtoString, null, emailControllerTemplateName, request);
		} catch (Exception e) { }


	}	
}
