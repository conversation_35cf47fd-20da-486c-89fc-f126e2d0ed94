package com.dell.it.eis.email.security;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.VelocityContext;
import java.io.StringWriter;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

import com.dell.it.eis.email.config.TemplateSecurityProperties;
import com.dell.it.eis.email.exception.TemplateSecurityException;

/**
 * Integration tests for template security with configurable properties
 */
@SpringBootTest(
    classes = TemplateSecurityIntegrationTest.TestConfig.class,
    properties = {
        "spring.cloud.config.enabled=false",
        "spring.cloud.config.import-check.enabled=false"
    }
)
class TemplateSecurityIntegrationTest {

    @Configuration
    @EnableConfigurationProperties(TemplateSecurityProperties.class)
    static class TestConfig {

        @Bean
        public TemplateSecurityValidator templateSecurityValidator() {
            return new TemplateSecurityValidator();
        }

        @Bean
        public SecureTemplateService secureTemplateService() {
            return new SecureTemplateService();
        }

        @Bean
        public VelocityEngine velocityEngine() {
            VelocityEngine mockEngine = mock(VelocityEngine.class);

            // Configure the mock to simulate successful template processing
            doAnswer(invocation -> {
                VelocityContext context = invocation.getArgument(0, VelocityContext.class);
                StringWriter writer = invocation.getArgument(1, StringWriter.class);
                String logTag = invocation.getArgument(2, String.class);
                String template = invocation.getArgument(3, String.class);

                // Simple template processing simulation
                String result = template;
                if (context != null) {
                    // Replace simple variables like $name with values from context
                    for (Object key : context.getKeys()) {
                        String keyStr = key.toString();
                        Object value = context.get(keyStr);
                        if (value != null) {
                            result = result.replace("$" + keyStr, value.toString());
                        }
                    }
                }
                writer.write(result);
                return true;
            }).when(mockEngine).evaluate(any(VelocityContext.class), any(StringWriter.class), anyString(), anyString());

            return mockEngine;
        }
    }

    @Autowired
    private TemplateSecurityValidator validator;

    @Autowired
    private SecureTemplateService secureTemplateService;

    @Autowired
    private TemplateSecurityProperties securityProperties;

    @Test
    void testSecuritySystemWithDefaultConfiguration() {
        // Test that the security system works with default configuration
        String safeTemplate = "Hello $name, your order #$orderNumber is ready!";
        Map<String, Object> model = new HashMap<>();
        model.put("name", "John Doe");
        model.put("orderNumber", "12345");

        // Validation should pass
        assertDoesNotThrow(() -> validator.validateTemplate(safeTemplate));

        // Template processing should work
        String result = secureTemplateService.processTemplateSecurely(safeTemplate, model);
        assertNotNull(result);
        assertTrue(result.contains("John Doe"));
        assertTrue(result.contains("12345"));
    }

    @Test
    void testSecuritySystemBlocksMaliciousTemplate() {
        String maliciousTemplate = "Hello ${Runtime.getRuntime()}";
        Map<String, Object> model = new HashMap<>();

        // Validation should fail
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        assertTrue(exception.getMessage().contains("dangerous content"));

        // Template processing should also fail
        TemplateSecurityException serviceException = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(maliciousTemplate, model)
        );
        assertTrue(serviceException.getMessage().contains("dangerous content"));
    }

    @SpringBootTest(
        classes = TemplateSecurityIntegrationTest.TestConfig.class,
        properties = {
            "spring.cloud.config.enabled=false",
            "spring.cloud.config.import-check.enabled=false",
            "template.security.max-size=1000",
            "template.security.max-nesting-depth=3",
            "template.security.processing-timeout=2000",
            "template.security.max-output-size=5000",
            "template.security.strict-validation=true",
            "template.security.log-security-violations=true"
        }
    )
    static class CustomConfigurationTest {

        @Autowired
        private TemplateSecurityValidator validator;

        @Autowired
        private SecureTemplateService secureTemplateService;

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testCustomSizeLimit() {
            // Verify custom configuration is loaded
            assertEquals(1000, securityProperties.getMaxSize());

            // Create template larger than custom limit
            StringBuilder largeTemplate = new StringBuilder();
            for (int i = 0; i < 1500; i++) {
                largeTemplate.append("a");
            }

            // Should fail with custom limit
            TemplateSecurityException exception = assertThrows(
                TemplateSecurityException.class,
                () -> validator.validateTemplate(largeTemplate.toString())
            );
            assertTrue(exception.getMessage().contains("1000 characters"));
        }

        @Test
        void testCustomNestingDepthLimit() {
            // Verify custom configuration is loaded
            assertEquals(3, securityProperties.getMaxNestingDepth());

            // Create template with nesting deeper than custom limit
            StringBuilder nestedTemplate = new StringBuilder();
            for (int i = 0; i < 5; i++) {
                nestedTemplate.append("#if($condition").append(i).append(")\n");
            }
            nestedTemplate.append("Hello World\n");
            for (int i = 0; i < 5; i++) {
                nestedTemplate.append("#end\n");
            }

            // Should fail with custom limit
            TemplateSecurityException exception = assertThrows(
                TemplateSecurityException.class,
                () -> validator.validateTemplate(nestedTemplate.toString())
            );
            assertTrue(exception.getMessage().contains("nesting depth exceeds"));
        }

        @Test
        void testCustomOutputSizeLimit() {
            // Verify custom configuration is loaded
            assertEquals(5000, securityProperties.getMaxOutputSize());

            String template = "Hello $name";
            Map<String, Object> model = new HashMap<>();
            
            // Create a large name that would result in output > 5000 characters
            StringBuilder largeName = new StringBuilder();
            for (int i = 0; i < 6000; i++) {
                largeName.append("a");
            }
            model.put("name", largeName.toString());

            // Should fail due to output size limit
            TemplateSecurityException exception = assertThrows(
                TemplateSecurityException.class,
                () -> secureTemplateService.processTemplateSecurely(template, model)
            );
            assertTrue(exception.getMessage().contains("output exceeds maximum size"));
        }
    }

    @SpringBootTest(
        classes = TemplateSecurityIntegrationTest.TestConfig.class,
        properties = {
            "spring.cloud.config.enabled=false",
            "spring.cloud.config.import-check.enabled=false",
            "template.security.strict-validation=false",
            "template.security.log-security-violations=false"
        }
    )
    static class NonStrictModeTest {

        @Autowired
        private TemplateSecurityValidator validator;

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testNonStrictMode() {
            // Verify non-strict mode is configured
            assertFalse(securityProperties.getStrictValidation());
            assertFalse(securityProperties.getLogSecurityViolations());

            // Malicious template should not throw exception in non-strict mode
            String maliciousTemplate = "Hello ${Runtime.getRuntime()}";
            assertDoesNotThrow(() -> validator.validateTemplate(maliciousTemplate));
        }
    }

    @Test
    void testConfigurationValidation() {
        // Test that configuration validation works
        assertDoesNotThrow(() -> securityProperties.validateConfiguration());
        
        // Verify configuration values are within expected ranges
        assertTrue(securityProperties.getMaxSizeSafe() >= 1000);
        assertTrue(securityProperties.getMaxNestingDepthSafe() >= 1);
        assertTrue(securityProperties.getProcessingTimeoutSafe() >= 1000L);
        assertTrue(securityProperties.getMaxOutputSizeSafe() >= 10000);
    }
}
