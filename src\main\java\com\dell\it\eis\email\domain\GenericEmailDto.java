package com.dell.it.eis.email.domain;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class GenericEmailDto {

	public String getSubject() {
		return subject;
	}
	public void setSubject(String subject) {
		this.subject = subject;
	}
	public String getBody() {
		return body;
	}
	public void setBody(String body) {
		this.body = body;
	}
	public String getToAddress() {
		return toAddress;
	}
	public void setToAddress(String toAddress) {
		this.toAddress = toAddress;
	}
	public String getFromAddress() {
		return fromAddress;
	}
	public void setFromAddress(String fromAddress) {
		this.fromAddress = fromAddress;
	}
	public String getCcAddress() {
		return ccAddress;
	}
	public void setCcAddress(String ccAddress) {
		this.ccAddress = ccAddress;
	}
	public List<Map<String, Object>> getTemplateMap() {
		return templateMap;
	}
	public void setTemplateMap(List<Map<String, Object>> templateMap) {
		this.templateMap = templateMap;
	}
	private String subject;
	private String body;
	private String toAddress;
	private String fromAddress;
	private String ccAddress;
	private List<Map<String, Object>> templateMap;	
	
}
