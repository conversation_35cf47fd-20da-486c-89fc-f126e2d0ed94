package com.dell.it.eis.email.service.impl;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;

import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.exception.CannotSendEmailException;
import com.dell.it.eis.email.exception.TemplateNotFoundException;
import com.dell.it.eis.email.exception.TemplateSecurityException;
import com.dell.it.eis.email.security.SecureTemplateService;
import com.dell.it.eis.email.service.EmailService;
import com.dell.it.eis.email.service.RedisFeignClient;
import com.dell.it.eis.email.util.Constants;

@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

	@Autowired
	private JavaMailSender javaMailSender;
	@Autowired
	private Executor executor;

	@Autowired
	private RedisFeignClient feignClient;

	@Autowired
	private SecureTemplateService secureTemplateService;

	private static final String DEFAULT_EMAIL_TEMPLATE = "emailTemplate.vm";

	@Autowired
	private VelocityEngine velocityEngine;

	/**
	 * 
	 * @param velocityEngine
	 * @param templateLocation
	 * @param encoding
	 * @param model
	 * @param writer
	 * @throws Exception
	 */
	public void mergeTemplate(VelocityEngine velocityEngine, String templateLocation, String encoding,
			Map<String, Object> model, Writer writer) {

		VelocityContext velocityContext = new VelocityContext(model);

		velocityEngine.mergeTemplate(templateLocation, encoding, velocityContext, writer);

	}

	/**
	 * 
	 * @param velocityEngine
	 * @param templateLocation
	 * @param encoding
	 * @param model
	 * @return
	 * @throws Exception
	 */
	public String mergeTemplateIntoString(VelocityEngine velocityEngine, String templateLocation, String encoding,
			Map<String, Object> model) {

		StringWriter result = new StringWriter();
		mergeTemplate(velocityEngine, templateLocation, encoding, model, result);
		return result.toString();
	}

	/**
	 * For Redis one - SECURE VERSION
	 *
	 * @param template
	 * @param model
	 * @return
	 * @throws TemplateSecurityException if template validation fails
	 */
	public String mergeTemplateIntoStringRedis(String template,
			Map<String, Object> model) {

		log.debug("Processing Redis template securely");

		try {
			// Use secure template service to validate and process template
			return secureTemplateService.processTemplateSecurely(template, model);
		} catch (TemplateSecurityException e) {
			log.error("Template security validation failed for Redis template: {}", e.getMessage());
			throw new CannotSendEmailException("Email template processing failed due to security validation: " + e.getMessage(), e);
		} catch (Exception e) {
			log.error("Unexpected error processing Redis template: {}", e.getMessage(), e);
			throw new CannotSendEmailException("Email template processing failed: " + e.getMessage(), e);
		}
	}

	/**
	 * 
	 * Async method for the email service
	 * 
	 */
	@Override
	public void sendGenericEmailAsync(GenericEmailDto genericEmaildto, String templateName, MultipartFile file)
			throws InterruptedException, ExecutionException, AddressException, MessagingException {

		CompletableFuture.runAsync(() -> {

			try {
				sendAsyncEmail(genericEmaildto, templateName, file.getBytes(), file);
			} catch (AddressException e) {
				throw new IllegalStateException(e);
			} catch (MessagingException e) {
				throw new IllegalStateException(e);
			} catch (Exception e) {
				throw new IllegalStateException(e);
			}

			log.info("Request submitted for the email");

		}, executor);

	}

	/**
	 * 
	 */
	@Override
	public void sendGenericEmail(GenericEmailDto genericEmaildtoString, String genericEmailTemplateName, MultipartFile genericEmailFile)
			throws AddressException, MessagingException {
		log.info("Inside service layer method sendGenericEmail");
		try {
			
			MimeMessage genericEmialMimeMessage = javaMailSender.createMimeMessage();
			MimeMessageHelper genericEmailmsgHelper = new MimeMessageHelper(genericEmialMimeMessage, true, Constants.UTF_8);
			if (genericEmaildtoString.getFromAddress() != null && !genericEmaildtoString.getFromAddress().isEmpty()) {
				genericEmailmsgHelper.setFrom(new InternetAddress(genericEmaildtoString.getFromAddress()));
			}
			if (genericEmaildtoString.getToAddress() != null && !genericEmaildtoString.getToAddress().isEmpty()) {
				genericEmailmsgHelper.setTo(genericEmaildtoString.getToAddress().split(","));
			}
			if (genericEmaildtoString.getCcAddress() != null && !genericEmaildtoString.getCcAddress().isEmpty()) {
				genericEmailmsgHelper.setCc(genericEmaildtoString.getCcAddress().split(","));
			}
			if(genericEmailFile != null) {
			String fileName = genericEmailFile.getOriginalFilename();
			if (fileName != null && !genericEmailFile.isEmpty()) {
				log.info("Going to add the attachement for file path {}", genericEmailFile);
				genericEmailmsgHelper.addAttachment(fileName, genericEmailFile);
			}
			}
			genericEmailmsgHelper.setSubject(genericEmaildtoString.getSubject());
			setGenericEmailText(genericEmaildtoString, genericEmailTemplateName, genericEmailmsgHelper);
			javaMailSender.send(genericEmailmsgHelper.getMimeMessage());
		} catch (Exception ex) {
			log.error("Error while sending the mail in service layer {}", ex.getMessage());
			throw new CannotSendEmailException(ex.getMessage(), ex);
		}
	}

	private void setGenericEmailText(GenericEmailDto genericEmaildto, String templateName, MimeMessageHelper genericEmailmsgHelper) throws MessagingException {
		String genericEmailText = null;
		if (templateName != null && !templateName.isEmpty()) {
			Map<String, Object> genericEmailParams = new HashMap<>();
			genericEmaildto.getTemplateMap().forEach(genericEmailParams::putAll);
			log.info("Map value for velocity template is {} ", genericEmailParams);
			ResponseEntity<String> genericEmailTemplate = feignClient.getVelocityTemplate(templateName);
			if (genericEmailTemplate.getStatusCode() == HttpStatus.OK) {
				log.info("Value coming from redis is {}", genericEmailTemplate);
				String body = genericEmailTemplate.getBody();
				if ("DefaultTemplate".equalsIgnoreCase(body)) {
					log.error("Some exception in redis service, hence fallback is being triggered with default template");
					genericEmailText = triggerDefaultEmail(genericEmaildto);
				} else {
					log.info("Going to trigger the mail with template received from redis with key as {}", templateName);
					genericEmailText = this.mergeTemplateIntoStringRedis(genericEmailTemplate.getBody(), genericEmailParams);
					log.info("Text from template is {}", genericEmailText);
				}
			} else {
				log.info("Template does not exist in redis with key {} ! please recheck once!!!", templateName);
				throw new TemplateNotFoundException("Template not found");
			}				
		} else {
			genericEmailText = triggerDefaultEmail(genericEmaildto);
		}
		if(genericEmailText != null) {
			genericEmailmsgHelper.setText(genericEmailText, true);
		}		
	}

	private String triggerDefaultEmail(GenericEmailDto genericEmaildto) {
		String text;
		log.info("Using default email template to send email");
		Map<String, Object> params = new HashMap<>();
		params.put("content", genericEmaildto.getBody());
		text = this.mergeTemplateIntoString(velocityEngine, Constants.TEMPLATE + DEFAULT_EMAIL_TEMPLATE, Constants.UTF_8, params);
		return text;
	}

	public void sendAsyncEmail(GenericEmailDto genericEmaildto, String asyncEmailTemplateName, byte[] fileBytes,
			MultipartFile multiPartFile) throws AddressException, MessagingException {
		log.info("Inside service layer method sendGenericEmail");
		try {
			MimeMessage asyncEmailMimeMessage = javaMailSender.createMimeMessage();
			MimeMessageHelper asyncEmailMsgHelper = new MimeMessageHelper(asyncEmailMimeMessage, true, Constants.UTF_8);
			if (genericEmaildto.getFromAddress() != null && !genericEmaildto.getFromAddress().isEmpty()) {
				asyncEmailMsgHelper.setFrom(new InternetAddress(genericEmaildto.getFromAddress()));
			}
			if (genericEmaildto.getToAddress() != null && !genericEmaildto.getToAddress().isEmpty()) {
				asyncEmailMsgHelper.setTo(genericEmaildto.getToAddress().split(","));
			}
			if (genericEmaildto.getCcAddress() != null && !genericEmaildto.getCcAddress().isEmpty()) {
				asyncEmailMsgHelper.setCc(genericEmaildto.getCcAddress().split(","));
			}
			if(multiPartFile != null) {
			String asyncEmailMultipartileName = multiPartFile.getOriginalFilename();
			if (!ObjectUtils.isEmpty(fileBytes) && asyncEmailMultipartileName != null ) {
				log.info("Going to add the attachement for file path {}", multiPartFile);
				asyncEmailMsgHelper.addAttachment(asyncEmailMultipartileName, new ByteArrayResource(fileBytes));
			}
			}
			asyncEmailMsgHelper.setSubject(genericEmaildto.getSubject());
			setAsyncEmailText(genericEmaildto, asyncEmailTemplateName, asyncEmailMsgHelper);
			javaMailSender.send(asyncEmailMsgHelper.getMimeMessage());
		} catch (Exception exception) {
			log.error("Error while sending the mail in service layer {}", exception.getMessage());
			throw new CannotSendEmailException(exception.getMessage(), exception);
		}
	}

	private void setAsyncEmailText(GenericEmailDto genericEmaildto, String templateName, MimeMessageHelper msgHelper) throws MessagingException {
		String asyncEmialText = null;
		if (templateName != null && !templateName.isEmpty()) {
			Map<String, Object> params = new HashMap<>();
			genericEmaildto.getTemplateMap().forEach(params::putAll);
			log.info("Map value for velocity template is {} ", params);
			ResponseEntity<String> asyncEmailTemplate = feignClient.getVelocityTemplate(templateName);
			if (asyncEmailTemplate.getStatusCode() == HttpStatus.OK) {
				log.info("Value coming from redis is {}", asyncEmailTemplate);
				String asyncEmailBody = asyncEmailTemplate.getBody();
				if ("DefaultTemplate".equalsIgnoreCase(asyncEmailBody)) {
					log.error(
							"Some exception in redis service, hence fallback is being triggered with response of default template");
					asyncEmialText = triggerDefaultEmail(genericEmaildto);
				} else {
					log.info("Going to trigger the mail with template received from redis with key as {}", templateName);
					asyncEmialText = this.mergeTemplateIntoStringRedis(asyncEmailTemplate.getBody(), params);
					log.info("Text from template is {}", asyncEmialText);
				}
			} else {
				log.info("Template does not exist in redis with key {} ! please recheck once!!!", templateName);
				throw new TemplateNotFoundException("Template not found");
			}
		} else {
			asyncEmialText = triggerDefaultEmail(genericEmaildto);
		}
		if(asyncEmialText != null) {
			msgHelper.setText(asyncEmialText, true);
		}
	}

	@Override
	@Async("emailAsyncExecutor")
	public void sendEmailAsync(GenericEmailDto genericEmaildto, String templateName, MultipartFile file)
			throws AddressException, MessagingException, IOException {

		if (!ObjectUtils.isEmpty(file)) {
			sendAsyncEmail(genericEmaildto, templateName, file.getBytes(), file);
		} else {
			sendAsyncEmail(genericEmaildto, templateName, null, file);
		}

	}

}
