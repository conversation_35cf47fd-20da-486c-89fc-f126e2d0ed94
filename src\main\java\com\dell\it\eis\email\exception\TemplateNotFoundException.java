/**
 * 
 */
package com.dell.it.eis.email.exception;

import java.io.Serial;


import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.NOT_FOUND)
public class TemplateNotFoundException extends RuntimeException {

	/**
	 * 
	 */
	@Serial
	private static final long serialVersionUID = -6351583994627804393L;

	public TemplateNotFoundException() {
	}

	/**
	 * 
	 */
	public TemplateNotFoundException(String message) {
		super(message);
	}

	public TemplateNotFoundException(final String message, final Throwable cause) {
		super(message, cause);
	}
}
