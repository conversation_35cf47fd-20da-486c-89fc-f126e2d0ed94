package com.dell.it.eis.email.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import jakarta.mail.internet.MimeMessage;

import org.apache.velocity.app.VelocityEngine;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.security.SecureTemplateService;
import com.dell.it.eis.email.service.RedisFeignClient;
import com.dell.it.eis.email.service.ServiceStatus;


@ExtendWith(MockitoExtension.class)
class EmailServiceImplTest {

	@InjectMocks
	private EmailServiceImpl service;

	@Mock
	JavaMailSender javaMailSender;

	@Mock
	Executor executor;

	@Mock
	RedisFeignClient feignClient;

	@Mock
	SecureTemplateService secureTemplateService;

	@Mock
	VelocityEngine velocityEngine;

	@Test
	void mergeTemplate() {

		VelocityEngine engine = mock(VelocityEngine.class);
		when(engine.mergeTemplate(Mockito.nullable(String.class), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
		StringWriter writer = new StringWriter();
		service.mergeTemplate(engine, "test", "test", new HashMap(), writer);

		assertNotNull(service.mergeTemplateIntoString(engine, "test", "test", new HashMap()));
	}

	@Test
	void mergeTemplate1() {
		Map<String, Object> testModel = new HashMap<>();
		testModel.put("name", "test");

		when(secureTemplateService.processTemplateSecurely("test", testModel)).thenReturn("processed template");

		String result = service.mergeTemplateIntoStringRedis("test", testModel);
		assertEquals("processed template", result);

		verify(secureTemplateService).processTemplateSecurely("test", testModel);
	}

	@Test
	void mergeTemplateSecurityException() {
		Map<String, Object> testModel = new HashMap<>();
		testModel.put("name", "test");

		when(secureTemplateService.processTemplateSecurely("malicious template", testModel))
			.thenThrow(new com.dell.it.eis.email.exception.TemplateSecurityException("Dangerous content detected"));

		Exception exception = assertThrows(Exception.class, () -> {
			service.mergeTemplateIntoStringRedis("malicious template", testModel);
		});

		assertTrue(exception.getMessage().contains("security validation"));
		verify(secureTemplateService).processTemplateSecurely("malicious template", testModel);
	}

	@Test
	void sendAsyncEmail() {
		GenericEmailDto genericEmaildto = new GenericEmailDto();
		MultipartFile genericEmailFile = getMimeObject();
		try {
			MimeMessage asyncEmailMimeMessage = mock(MimeMessage.class);
			when(javaMailSender.createMimeMessage()).thenReturn(asyncEmailMimeMessage);

			doNothing().when(javaMailSender).send(Mockito.any(MimeMessage.class));

			ResponseEntity<String> asyncEmailTemplate = new ResponseEntity("DefaultTemplate", HttpStatus.OK); 
			when(feignClient.getVelocityTemplate(Mockito.any())).thenReturn(asyncEmailTemplate);

			assertNotNull(ServiceStatus.CLOSED);
			assertNotNull(ServiceStatus.CLOSING);
			assertNotNull(ServiceStatus.RUNNING);
			genericEmaildto.setSubject("test");
			List<Map<String, Object>> templateMap = new ArrayList<>();
			Map<String,Object> map = new HashMap<>();
			map.put("test", "test");
			templateMap.add(map);
			genericEmaildto.setTemplateMap(templateMap);
			genericEmaildto.setToAddress("test");
			genericEmaildto.setFromAddress("test");
			genericEmaildto.setCcAddress("test");
			service.sendAsyncEmail(genericEmaildto, "test", null, genericEmailFile);

			service.sendEmailAsync(genericEmaildto, null, genericEmailFile);

			doThrow(new NullPointerException()).when(javaMailSender).send(Mockito.any(MimeMessage.class));
			service.sendAsyncEmail(genericEmaildto, "test", null, genericEmailFile);

		} catch (Exception e) { }
	}

	@Test
	void sendGenericEmail() {

		GenericEmailDto genericEmaildto = new GenericEmailDto();
		try {
			MimeMessage asyncEmailMimeMessage = mock(MimeMessage.class);
			when(javaMailSender.createMimeMessage()).thenReturn(asyncEmailMimeMessage);

			doNothing().when(javaMailSender).send(Mockito.any(MimeMessage.class));
			ResponseEntity<String> asyncEmailTemplate = new ResponseEntity("DefaultTemplate", HttpStatus.OK); 
			when(feignClient.getVelocityTemplate(Mockito.any())).thenReturn(asyncEmailTemplate);

			genericEmaildto.setSubject("test");
			List<Map<String, Object>> templateMap = new ArrayList<>();
			Map<String,Object> map = new HashMap<>();
			map.put("test", "test");
			templateMap.add(map);
			genericEmaildto.setToAddress("test");
			genericEmaildto.setFromAddress("test");
			genericEmaildto.setCcAddress("test");
			genericEmaildto.setTemplateMap(templateMap);
			MultipartFile genericEmailFile = getMimeObject();
			service.sendGenericEmail(genericEmaildto, "test", genericEmailFile);
			doThrow(new NullPointerException()).when(javaMailSender).send(Mockito.any(MimeMessage.class));
			service.sendGenericEmail(genericEmaildto, "test", genericEmailFile);
		} catch (Exception e) { }
	}

	private MultipartFile getMimeObject() {
		return new MultipartFile() {

			@Override
			public void transferTo(File dest) throws IOException, IllegalStateException {

			}

			@Override
			public boolean isEmpty() {
				return false;
			}

			@Override
			public long getSize() {
				return 0;
			}

			@Override
			public String getOriginalFilename() {
				return null;
			}

			@Override
			public String getName() {
				return null;
			}

			@Override
			public InputStream getInputStream() throws IOException {
				return null;
			}

			@Override
			public String getContentType() {
				return null;
			}

			@Override
			public byte[] getBytes() throws IOException {
				return null;
			}
		};
	}
}
