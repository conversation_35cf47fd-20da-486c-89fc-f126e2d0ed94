package com.dell.it.eis.email.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;

import org.apache.velocity.app.VelocityEngine;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.service.RedisFeignClient;


@ExtendWith(MockitoExtension.class)
class EmailUtilTest {


	@Test
	void isValidExtension() {
		EmailUtil emailUtil = new EmailUtil();
		assertTrue(emailUtil.isValidExtension("test.vm"));
		assertFalse(emailUtil.isValidExtension("test.txt"));


	}

	@Test
	void validateRequest() {
		EmailUtil emailUtil = new EmailUtil();
		GenericEmailDto genericEmailDto = new GenericEmailDto();
		genericEmailDto.setToAddress("test");
		genericEmailDto.setFromAddress(null);	

		try {
			emailUtil.validateRequest(genericEmailDto, "test");

		} catch (Exception e) { }

		try {
			genericEmailDto.setFromAddress("test");
			emailUtil.validateRequest(genericEmailDto, "test.txt");
		} catch (Exception e) { }
	}
}
