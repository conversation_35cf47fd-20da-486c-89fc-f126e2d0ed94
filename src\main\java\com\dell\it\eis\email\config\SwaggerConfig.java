package com.dell.it.eis.email.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

/**
 * 
 * <AUTHOR>
 *
 */
@Configuration
public class SwaggerConfig {

	@Bean
	GroupedOpenApi publicApi() {
		return GroupedOpenApi.builder().group("public").pathsToMatch("/*.*").build();
	}

	@Bean
	OpenAPI runtimeMaturityAPI() {
		return new OpenAPI().info(new Info().title("PCF Email Service API")
				.description("API for sending Emails").version("1.0").license(new License()
						.name("Apache License Version 2.0").url("https://www.apache.org/licenses/LICENSE-2.0")));

	}

}
