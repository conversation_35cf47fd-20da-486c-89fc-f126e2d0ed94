package com.dell.it.eis.email.controller;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.ApiResponse;
import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.exception.BadRequestException;
import com.dell.it.eis.email.exception.CannotSendEmailException;
import com.dell.it.eis.email.service.EmailService;
import com.dell.it.eis.email.util.EmailUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

@RestController
@RefreshScope
@Slf4j
public class EmailAsyncContoller {

	@Autowired
	EmailService emailService;

	@Autowired
	EmailUtil emailUtil;

	private static final String SUCCESS_STATUS = "Mail request submitted successfully";

	private SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:MM:SS");

	/**
	 * The below API will be used to send a mail based on the template name and
	 * template value provided in the request.
	 * 
	 * Here we need to pass reqjson as a file attachement
	 * 
	 * @param genericEmailDto
	 * @param templateName
	 * @return ResponseEntity<ApiResponse>
	 * @throws Exception
	 * @throws MessagingException
	 * @throws AddressException
	 * @throws IOException 
	 * 
	 */
	@PostMapping(value = "/api/v1/async/generic/email", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<ApiResponse> sendGenericEmailAsyn(@RequestPart("reqjson") GenericEmailDto genericEmailDto,
			@RequestPart(name = "file", required = false) MultipartFile file,
			@RequestParam(name = "template", required = false) String templateName, HttpServletRequest request) throws AddressException, MessagingException, IOException
			{

		log.info("Going to trigger the email for email address {}", genericEmailDto.getToAddress());
		ResponseEntity<ApiResponse> response = null;
		emailUtil.validateRequest(genericEmailDto, templateName);
		try {
			emailService.sendEmailAsync(genericEmailDto, templateName, file);
			log.info("Email Request submitted successfully");
			response = new ResponseEntity<>(new ApiResponse(dateFormat.format(new Date()), SUCCESS_STATUS,
					"to " + genericEmailDto.getToAddress()), HttpStatus.OK);
		} catch (CannotSendEmailException ex) {
			ex.printStackTrace();
			log.error("Error while sending mail {}", ex.getMessage());
			throw new CannotSendEmailException(ex.getMessage(), ex);
		}
		return response;
	}

	
	@PostMapping(value = "/api/v2/async/generic/email", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<ApiResponse> sendGenericEmailAsyn(@RequestPart("reqjson") String genericEmailDtoString,
			@RequestPart(name = "file", required = false) MultipartFile multiPartFile,
			@RequestParam(name = "template", required = false) String templateNameString, HttpServletRequest httpServletRequest)
			throws AddressException, MessagingException, IOException {

		log.info("Going to trigger the email for email address {}");
		ResponseEntity<ApiResponse> responseEntityResponse = null;
		GenericEmailDto emailDto = null;
		try {
			emailDto = new ObjectMapper().readValue(genericEmailDtoString, GenericEmailDto.class);
		} catch (Exception ex) {
			log.error("Error while converting string json to DTO. Json Passed is {}", genericEmailDtoString);
			throw new BadRequestException(
					"Error while converting string json to DTO. Please check the json fields properly.");

		}
		emailUtil.validateRequest(emailDto, templateNameString);
		try {
			emailService.sendEmailAsync(emailDto, templateNameString, multiPartFile);
			log.info("Email Request submitted successfully");
			responseEntityResponse = new ResponseEntity<>(new ApiResponse(dateFormat.format(new Date()), SUCCESS_STATUS,
					"to " + emailDto.getToAddress()), HttpStatus.OK);
		} catch (CannotSendEmailException exc) {
			exc.printStackTrace();
			log.error("Error while sending mail {}", exc.getMessage());
			throw new CannotSendEmailException(exc.getMessage(), exc);
		}
		return responseEntityResponse;
	}

}
