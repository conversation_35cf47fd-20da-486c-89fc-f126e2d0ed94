<!DOCTYPE html>
<html>
<head>
<style>
table, th, td {
	border: 1px solid black;
	border-collapse: collapse;
}

th, td {
	padding: 10px;
	text-align: left;
}
</style>
</head>
<body>

	<div class="container">

		<div class="row">
			<div class="col-lg-12">
				<p style="margin-top: 3%;">
					Your Glovia request has been approved for the trading partner
					"${partner_partyName}" by "${approver}" and Integration has been
					configured, You can begin the testing. <br> Any queries reach
					out to ${contactEmail}<br>
				</p>
			</div>
		</div>

		<h2 style="margin-left: 32%; color: blue;">Glovia Supplier
			Onboarding</h2>

		<font size="3" face="Serif">
			<table style="width: 70%;margin-left: 12%">
			
				<tr style="background-color: #f2f2f2;">
					<th>Trading Partner name</th>

					<td>${partner_partyName}</td>

				</tr>
				<tr>
					<th>Trading Partner Business email</th>

					<td>${primaryContact_email}</td>

				</tr>
				<tr style="background-color: #f2f2f2;">
					<th>Trading Partner IT email</th>

					<td>${tpItEmail}</td>

				</tr>
				<tr>
					<th>Trading Partner Support Email</th>

					<td>${tpSupportEmail}</td>

				</tr>

				<tr style="background-color: #f2f2f2;">
					<th>File Format</th>

					<td>${messagingId_class}</td>

				</tr>
				<tr>
					<th>Partner connectivity to Dell</th>

					<td>${pickup_format}</td>

				</tr>

				<tr style="background-color: #f2f2f2;">
					<th>Partner ISA Qualifier</th>

					<td>${messagingId_interchangeIdQualifier}</td>

				</tr>

				<tr>
					<th>Partner ISA ID</th>

					<td>${messagingId_interchangeId}</td>

				</tr>

				<tr style="background-color: #f2f2f2;">
					<th>Partner GS ID</th>

					<td>${messagingId_applicationId}</td>

				</tr>

				<tr>
					<th>AS2 URL</th>

					<td>${delivery_url}</td>

				</tr>

				<tr style="background-color: #f2f2f2;">
					<th>AS2 ID</th>

					<td>${partner_routingId}</td>

				</tr>

				<tr>
					<th>Partner Region</th>

					<td>${location}</td>

				</tr>


				<tr style="background-color: #f2f2f2;">
					<th>Messaging Id</th>

					<td>${messagingId_identification}</td>

				</tr>


				<tr>

					<th>CCN</th>

					<td>${ccn}</td>

				</tr>
				
			</table>
	</div>

</body>
</html>
