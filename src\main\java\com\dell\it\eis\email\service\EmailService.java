package com.dell.it.eis.email.service;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;

import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.GenericEmailDto;

public interface EmailService {

	/**
	 * Synchronous Method for mail
	 * 
	 * @param genericEmaildto
	 * @param templateName
	 * @param file
	 * @throws AddressException
	 * @throws MessagingException
	 * @throws Exception
	 */
	public void sendGenericEmail(GenericEmailDto genericEmaildto, String templateName, MultipartFile file)
			throws AddressException, MessagingException;
	
	/**
	 * Spring Boot Default Async Annotation
	 * 
	 * @param genericEmaildto
	 * @param templateName
	 * @param file
	 * @throws AddressException
	 * @throws MessagingException
	 * @throws IOException 
	 * @throws Exception
	 */
	public void sendEmailAsync(GenericEmailDto genericEmaildto, String templateName, MultipartFile file)
			throws AddressException, MessagingException, IOException;
	
	
	/**
	 * Using Custom Completable Future
	 * 
	 * @param genericEmaildto
	 * @param templateName
	 * @param file
	 * @throws InterruptedException
	 * @throws ExecutionException
	 * @throws AddressException
	 * @throws MessagingException
	 * @throws Exception
	 */
	public void sendGenericEmailAsync(GenericEmailDto genericEmaildto, String templateName, MultipartFile file) throws InterruptedException, ExecutionException,AddressException, MessagingException;

}
