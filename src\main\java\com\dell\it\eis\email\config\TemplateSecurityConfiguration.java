package com.dell.it.eis.email.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import lombok.extern.slf4j.Slf4j;

/**
 * Configuration class for template security initialization
 */
@Configuration
@Slf4j
public class TemplateSecurityConfiguration {

    @Autowired
    private TemplateSecurityProperties securityProperties;

    /**
     * Validates and logs the security configuration after application startup
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateSecurityConfiguration() {
        log.info("Initializing Template Security Configuration...");
        
        try {
            securityProperties.validateConfiguration();
            log.info("Template Security Configuration validated successfully");
        } catch (Exception e) {
            log.error("Failed to validate Template Security Configuration", e);
            throw new IllegalStateException("Invalid template security configuration", e);
        }
    }
}
