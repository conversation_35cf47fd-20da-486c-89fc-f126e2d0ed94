spring:
  application:
    name: ${app_config_name}
  main:
    allow-bean-definition-overriding: true 
  config.activate.on-profile: ${SPRING_PROFILES_ACTIVE}   
  config.import: optional:configserver:${configserver_uri}    
management:
  endpoints:
     web:
       exposure:
         include: '*'
         exclude: 'beans'

# Template Security Configuration
template:
  security:
    # Maximum template size in characters (default: 50KB)
    max-size: 50000
    # Maximum nesting depth for Velocity constructs (default: 10)
    max-nesting-depth: 10
    # Processing timeout in milliseconds (default: 5 seconds)
    processing-timeout: 5000
    # Maximum output size in characters (default: 500KB)
    max-output-size: 500000
    # Whether to enable strict validation mode (default: true)
    strict-validation: true
    # Whether to log security violations (default: true)
    log-security-violations: true