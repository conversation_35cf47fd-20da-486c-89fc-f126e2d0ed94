<!DOCTYPE html>
<html>

<head>
  <style>
    body {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    table {
      width:750px;
      border-bottom: red;
      border-spacing: 30px 30px;
      padding: 0 100px 100px 0;
      border-collapse: collapse;
    }

    th,
    td {
      padding: 20px;
      text-align: left;
      border-bottom: 2px solid #ddd;
    }

    .titleBar {
      display: flex;
      flex-direction: column;
      background-color: #007DB8;
      position: relative;
      height:80px;
      width:800px;
      margin-top: 20px;
      text-align: center;
    }

    .title {
      display: flex;
      justify-content: center;
    }
    .title > h2 {
      margin: 0px 0px 4px 0px;
      color: #ffffff;
      text-align: center;
    }

    img {
      width: 40px;
      height: 40px;
      margin: 4px 0px 0px 4px;
      left: 0;
    }
  </style>
</head>


<body>
 <!--   <div class="titleBar">
     <img src="white-dell-logo-transparent2.png" alt="">
    <div class="title">
      <h2>Secure Transport Account Management</h2>
    </div>
  </div> -->
  <div>
  <div style=" background-color: #007DB8;height:150px;width:600px;margin-left:20%;margin-right:20%">
  
<h1 style="color: #5e9ca0;">&nbsp;</h1>
<h2 style="color: #ffffff; text-align: center" >MQ Management</h2>
<h2 style="color: #2e6c80;">&nbsp;</h2>
</div>
  
  <div style="text-align:center;">
    <table>
      <tr>
        <td width="25%"><b>Source Host Name :</b></td>
        <td width="22%"> ${srcHost} </td>
		<td width="28%"><b>Destination Host Name :</b></td>
        <td width="25%"> ${destHost} </td>
      </tr>
      <tr>
        <td><b>Source Queue Manager :</b></td>
        <td>${srcQMgr}</td>
		<td><b>Destination Queue Manager :</b></td>
        <td>${destQMgr}</td>
      </tr>
      <tr>
        <td><b>Source MQ Port :</b></td>
        <td>${srcPort}</td>
		<td><b>Destination MQ Port :</b></td>
        <td>${destPort}</td>
      </tr>
	 <tr>${queues}</tr>
	
    </table>
  </div> 
  <br><br>
  
  Thanks,<br/>
  EIS Team
  </div>
</body>


</html>