package com.dell.it.eis.email.security;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import com.dell.it.eis.email.config.TemplateSecurityProperties;
import com.dell.it.eis.email.exception.TemplateSecurityException;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TemplateSecurityValidatorTest {

    @Mock
    private TemplateSecurityProperties securityProperties;

    @InjectMocks
    private TemplateSecurityValidator validator;

    @BeforeEach
    void setUp() {
        // Set up default mock behavior
        when(securityProperties.getMaxSizeSafe()).thenReturn(50000);
        when(securityProperties.getMaxNestingDepthSafe()).thenReturn(10);
        when(securityProperties.getLogSecurityViolations()).thenReturn(true);
        when(securityProperties.getStrictValidation()).thenReturn(true);
    }

    @Test
    void testValidateTemplate_SafeTemplate_ShouldPass() {
        String safeTemplate = "Hello $name, your order #$orderNumber is ready!";
        
        assertDoesNotThrow(() -> validator.validateTemplate(safeTemplate));
    }

    @Test
    void testValidateTemplate_EmptyTemplate_ShouldPass() {
        assertDoesNotThrow(() -> validator.validateTemplate(""));
        assertDoesNotThrow(() -> validator.validateTemplate(null));
        assertDoesNotThrow(() -> validator.validateTemplate("   "));
    }

    @Test
    void testValidateTemplate_ClassAccess_ShouldFail() {
        String maliciousTemplate = "Hello $name.class";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_GetClassMethod_ShouldFail() {
        String maliciousTemplate = "Hello ${user.getClass()}";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_RuntimeAccess_ShouldFail() {
        String maliciousTemplate = "Hello ${Runtime.getRuntime()}";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_SystemAccess_ShouldFail() {
        String maliciousTemplate = "Hello ${System.exit(0)}";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_EvaluateDirective_ShouldFail() {
        String maliciousTemplate = "#evaluate('malicious code')";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_IncludeDirective_ShouldFail() {
        String maliciousTemplate = "#include('/etc/passwd')";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_ReflectionAccess_ShouldFail() {
        String maliciousTemplate = "Hello ${obj.getDeclaredMethod('exec')}";
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        
        assertTrue(exception.getMessage().contains("dangerous content"));
    }

    @Test
    void testValidateTemplate_ExcessiveSize_ShouldFail() {
        StringBuilder largeTemplate = new StringBuilder();
        for (int i = 0; i < 60000; i++) {
            largeTemplate.append("a");
        }

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(largeTemplate.toString())
        );

        assertTrue(exception.getMessage().contains("exceeds maximum allowed size"));
        verify(securityProperties).getMaxSizeSafe();
        verify(securityProperties).getLogSecurityViolations();
    }

    @Test
    void testValidateTemplate_ConfigurableSize_ShouldUseCustomLimit() {
        // Set custom size limit
        when(securityProperties.getMaxSizeSafe()).thenReturn(1000);

        StringBuilder template = new StringBuilder();
        for (int i = 0; i < 1500; i++) {
            template.append("a");
        }

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(template.toString())
        );

        assertTrue(exception.getMessage().contains("1000 characters"));
        verify(securityProperties).getMaxSizeSafe();
    }

    @Test
    void testValidateTemplate_ExcessiveNesting_ShouldFail() {
        StringBuilder nestedTemplate = new StringBuilder();
        for (int i = 0; i < 15; i++) {
            nestedTemplate.append("#if($condition").append(i).append(")\n");
        }
        nestedTemplate.append("Hello World\n");
        for (int i = 0; i < 15; i++) {
            nestedTemplate.append("#end\n");
        }
        
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(nestedTemplate.toString())
        );
        
        assertTrue(exception.getMessage().contains("nesting depth exceeds"));
        verify(securityProperties).getMaxNestingDepthSafe();
        verify(securityProperties).getLogSecurityViolations();
        verify(securityProperties).getStrictValidation();
    }

    @Test
    void testValidateTemplate_ConfigurableNestingDepth_ShouldUseCustomLimit() {
        // Set custom nesting depth limit
        when(securityProperties.getMaxNestingDepthSafe()).thenReturn(3);

        StringBuilder nestedTemplate = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            nestedTemplate.append("#if($condition").append(i).append(")\n");
        }
        nestedTemplate.append("Hello World\n");
        for (int i = 0; i < 5; i++) {
            nestedTemplate.append("#end\n");
        }

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(nestedTemplate.toString())
        );

        assertTrue(exception.getMessage().contains("nesting depth exceeds"));
        assertTrue(exception.getMessage().contains("3"));
        verify(securityProperties).getMaxNestingDepthSafe();
    }

    @Test
    void testValidateTemplate_NonStrictMode_ShouldNotThrowForDangerousPatterns() {
        // Disable strict validation
        when(securityProperties.getStrictValidation()).thenReturn(false);

        String maliciousTemplate = "Hello ${Runtime.getRuntime()}";

        // Should not throw exception in non-strict mode
        assertDoesNotThrow(() -> validator.validateTemplate(maliciousTemplate));

        verify(securityProperties).getStrictValidation();
        verify(securityProperties).getLogSecurityViolations();
    }

    @Test
    void testValidateTemplate_DisabledLogging_ShouldNotLog() {
        // Disable security violation logging
        when(securityProperties.getLogSecurityViolations()).thenReturn(false);

        String maliciousTemplate = "Hello $name.class";

        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );

        assertTrue(exception.getMessage().contains("dangerous content"));
        verify(securityProperties).getLogSecurityViolations();
        verify(securityProperties).getStrictValidation();
    }

    @Test
    void testSanitizeTemplate_RemovesDangerousDirectives() {
        String maliciousTemplate = "Hello #evaluate('bad code') and #include('file')";
        
        String sanitized = validator.sanitizeTemplate(maliciousTemplate);
        
        assertFalse(sanitized.contains("#evaluate"));
        assertFalse(sanitized.contains("#include"));
        assertTrue(sanitized.contains("REMOVED"));
    }

    @Test
    void testSanitizeTemplate_EscapesClassAccess() {
        String maliciousTemplate = "Hello $obj.class and $obj.getClass()";
        
        String sanitized = validator.sanitizeTemplate(maliciousTemplate);
        
        assertFalse(sanitized.contains(".class"));
        assertFalse(sanitized.contains(".getClass("));
        assertTrue(sanitized.contains("CLASS_REMOVED"));
    }

    @Test
    void testSanitizeTemplate_EmptyInput_ReturnsInput() {
        assertNull(validator.sanitizeTemplate(null));
        assertEquals("", validator.sanitizeTemplate(""));
        assertEquals("   ", validator.sanitizeTemplate("   "));
    }
}
