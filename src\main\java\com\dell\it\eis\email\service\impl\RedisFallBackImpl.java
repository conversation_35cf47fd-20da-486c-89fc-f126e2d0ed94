package com.dell.it.eis.email.service.impl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.dell.it.eis.email.service.RedisFeignClient;

import feign.FeignException;

/**
 * 
 * <AUTHOR>
 *
 */

@Slf4j
public class RedisFallBackImpl implements RedisFeignClient {

	private final Throwable cause;

	public RedisFallBackImpl(Throwable cause) {
		this.cause = cause;
	}

	@Override
	public ResponseEntity<String> getVelocityTemplate(String key) {

		if (cause instanceof FeignException exception && exception.status() == 404) {

			log.error("Error logged as : " + cause.getMessage());

		}
		return new ResponseEntity<>("DefaultTemplate", HttpStatus.OK);
	}

}
