variables:
  SONAR_EXCLUSIONS: '**/AicEmailApiApplication.java,**/controller/HomeController,**/config/*.java,**/domain/*.java,**/exception/*.java,**/impl/RedisFallBackImpl.java,**/impl/RedisFallBackFactory.java,**/config/*.java'
  DEV_PCF_ORG: AICCoreDevOrg
  DEV_PCF_SPACE: aicshd-cit-dev
  PCF_MF_FILE_DEV: manifest-dev-oauth-dtc.yml
  PCF_MF_FILE_DES: manifest-des-oauth-dtc.yml
  PCF_MF_FILE_QAS: manifest-qas-oauth-dtc.yml
  PCF_MF_FILE_QAX: manifest-qax-oauth-dtc.yml
  PCF_MF_FILE_QAV: manifest-qav-oauth-dtc.yml 
  PCF_MF_FILE_PERF: manifest-prf-oauth-dtc.yml
  PCF_MF_FILE_PROD: manifest-prod-dtc.yml
  PCF_MF_FILE_DR: manifest-prod-dtc.yml
  manifest_appid: 53153
  manifest_appid_prod: 80727
  Actmon_logging_clientrmq: 'no'
  Package_Type: war
  ContivoSupport: 'no'
  Gemfire_connectivity: 'no'
include:
- project: ServiceAICNP/shared-template
  ref: common-template
  file: aic/aic-realtime-template.yml
  
