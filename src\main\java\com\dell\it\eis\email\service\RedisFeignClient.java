package com.dell.it.eis.email.service;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.dell.it.eis.email.service.impl.RedisFallBackFactory;
import com.emc.it.eis.oauth2client.OAuth2FeignAutoConfiguration;

@FeignClient(name = "redis-service", url = "${redis.service.url}", fallbackFactory  = RedisFallBackFactory.class,configuration = OAuth2FeignAutoConfiguration.class)
public interface RedisFeignClient {

	@GetMapping("/api/redis/template")
	public ResponseEntity<String> getVelocityTemplate(@RequestParam String key);

}
