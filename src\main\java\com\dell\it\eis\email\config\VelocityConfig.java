package com.dell.it.eis.email.config;

import java.util.Properties;

import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import lombok.extern.slf4j.Slf4j;

/**
 * Secure Velocity Engine configuration to prevent code injection attacks
 */
@Configuration
@Slf4j
public class VelocityConfig {

    /**
     * Creates a securely configured Velocity Engine
     */
    @Bean
    @Primary
    public VelocityEngine secureVelocityEngine() {
        log.info("Configuring secure Velocity Engine");
        
        VelocityEngine velocityEngine = new VelocityEngine();
        Properties properties = new Properties();
        
        // Security configurations
        
        // 1. Disable dangerous directives
        properties.setProperty("directive.evaluate.enable", "false");
        properties.setProperty("directive.include.enable", "false");
        properties.setProperty("directive.parse.enable", "false");
        
        // 2. Restrict introspection to prevent access to dangerous methods
        properties.setProperty(RuntimeConstants.INTROSPECTOR_RESTRICT_PACKAGES, 
            "java.lang.reflect,java.lang.Runtime,java.lang.System,java.lang.ProcessBuilder," +
            "java.io,java.nio,java.net,javax.script,java.lang.Class");
        
        // 3. Restrict class access
        properties.setProperty(RuntimeConstants.INTROSPECTOR_RESTRICT_CLASSES,
            "java.lang.Class,java.lang.ClassLoader,java.lang.Runtime,java.lang.System," +
            "java.lang.ProcessBuilder,java.lang.Thread,java.security.AccessController");
        
        // 4. Set resource loading restrictions
        properties.setProperty(RuntimeConstants.RESOURCE_LOADER, "string");
        properties.setProperty("string.resource.loader.class", 
            "org.apache.velocity.runtime.resource.loader.StringResourceLoader");
        
        // 5. Disable file resource loader to prevent file system access
        properties.setProperty("file.resource.loader.enable", "false");
        
        // 6. Set encoding
        properties.setProperty(RuntimeConstants.INPUT_ENCODING, "UTF-8");
        properties.setProperty("output.encoding", "UTF-8");

        // 7. Performance and security limits
        properties.setProperty("directive.foreach.maxloops", "100");
        properties.setProperty("runtime.references.strict", "true");

        // 8. Logging configuration
        properties.setProperty("runtime.log.logsystem.class",
            "org.apache.velocity.runtime.log.NullLogChute");
        
        // 9. Parser configuration for security
        properties.setProperty("parser.pool.size", "1");
        properties.setProperty("velocimacro.library.autoreload", "false");
        properties.setProperty("velocimacro.permissions.allow.inline", "false");
        properties.setProperty("velocimacro.permissions.allow.inline.to.replace.global", "false");
        
        // 10. Strict reference mode
        properties.setProperty("runtime.references.strict", "true");
        properties.setProperty("runtime.references.strict.escape", "true");
        
        try {
            velocityEngine.init(properties);
            log.info("Secure Velocity Engine configured successfully");
        } catch (Exception e) {
            log.error("Failed to initialize secure Velocity Engine", e);
            throw new RuntimeException("Failed to initialize secure Velocity Engine", e);
        }
        
        return velocityEngine;
    }
}
