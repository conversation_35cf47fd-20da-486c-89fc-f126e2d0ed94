package com.dell.it.eis.email.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.exception.BadRequestException;

@Component
@Slf4j
public class EmailUtil {

	/**
	 * 
	 * @param fileName
	 * @return
	 */
	public boolean isValidExtension(String fileName) {
		final String extension = FilenameUtils.getExtension(fileName);
		return "vm".equals(extension);
	}

	/**
	 * 
	 * @param genericEmailDto
	 * @param templateName
	 */
	public void validateRequest(GenericEmailDto genericEmailDto, String templateName) {
		if (ObjectUtils.isEmpty(genericEmailDto.getFromAddress())
				|| ObjectUtils.isEmpty(genericEmailDto.getToAddress())) {

			log.info("Either from address or To Address is emthy or null");
			throw new BadRequestException("Either from address or To Address is emthy or null. Please check again.");
		}

		if (!ObjectUtils.isEmpty(templateName) && !isValidExtension(templateName)) {
				log.debug("Invalid file extension {}", templateName);
				throw new BadRequestException("Invalid file extension. Please provide velocity template with vm extention");
		}

	}

}
