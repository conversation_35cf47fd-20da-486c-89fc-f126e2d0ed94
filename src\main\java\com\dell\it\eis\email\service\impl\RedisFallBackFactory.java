package com.dell.it.eis.email.service.impl;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import com.dell.it.eis.email.service.RedisFeignClient;


@Component
public class RedisFallBackFactory implements FallbackFactory<RedisFeignClient> {

	@Override
	public RedisFeignClient create(Throwable throwable) {
		  return new RedisFallBackImpl(throwable);
	}

   
}
