openapi: 3.0.1
info:
  title: PCF Email Service API
  description: API for sending Emails
  license:
    name: Apache License Version 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  version: '1.0'
servers:
  - url: https://email-service-shared-oauth.perf.aic.us.dell.com
    description: Generated server url
paths:
  /api/v2/generic/email:
    post:
      tags:
        - email-controller
      operationId: sendGenericEmailAsyn
      parameters:
        - name: template
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - reqjson
              type: object
              properties:
                reqjson:
                  type: string
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/v2/async/generic/email:
    post:
      tags:
        - email-async-contoller
      operationId: sendGenericEmailAsyn_1
      parameters:
        - name: template
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - reqjson
              type: object
              properties:
                reqjson:
                  type: string
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/v1/generic/email:
    post:
      tags:
        - email-controller
      operationId: sendGenericEmail
      parameters:
        - name: template
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - reqjson
              type: object
              properties:
                reqjson:
                  $ref: '#/components/schemas/GenericEmailDto'
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/v1/async/generic/email:
    post:
      tags:
        - email-async-contoller
      operationId: sendGenericEmailAsyn_2
      parameters:
        - name: template
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - reqjson
              type: object
              properties:
                reqjson:
                  $ref: '#/components/schemas/GenericEmailDto'
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponse'
components:
  schemas:
    ApiResponse:
      type: object
      properties:
        timestamp:
          type: string
        message:
          type: string
        details:
          type: string
    GenericEmailDto:
      type: object
      properties:
        subject:
          type: string
        body:
          type: string
        toAddress:
          type: string
        fromAddress:
          type: string
        ccAddress:
          type: string
        templateMap:
          type: array
          items:
            type: object
            additionalProperties:
              type: object

