<!DOCTYPE html>
<html>

<head>
<style>
body {
	display: flex;
	flex-direction: column;
	align-items: center;
}

table {
	width: 100%;
	border-bottom: red;
	border-spacing: 30px 30px;
	padding: 50px 100px 100px 50px;
	border-collapse: collapse;
	table-layout: auto;
}

th, td {
	padding: 10px;
	text-align: left;
	border-bottom: 2px solid #ddd;
}

.titleBar {
	display: flex;
	flex-direction: column;
	background-color: #007DB8;
	position: relative;
	height: 80px;
	width: 800px;
	margin-top: 20px;
	text-align: center;
}

.title {
	display: flex;
	justify-content: center;
}

.title>h2 {
	margin: 0px 0px 4px 0px;
	color: #ffffff;
	text-align: center;
}

img {
	width: 40px;
	height: 40px;
	margin: 4px 0px 0px 4px;
	left: 0;
}
</style>
</head>

<body>
	<div>
		<div
			style="background-color: #007DB8; height: 50px; width: 500px; margin-left: 20%; margin-right: 20%; margin-bottom: 5%">

			<h2 style="color: #5e9ca0; text-align: center"></h2>
			<h2 style="color: #ffffff; text-align: center">${productName}
				New Domain Details</h2>
			<h2 style="color: #2e6c80;">&nbsp;</h2>
		</div>

		<div style="text-align: center;">
			<table>
				<tr>
					<td width="30%"><b>Domain Name :</b></td>
					<td width="70%">${domainName}</td>
				</tr>
				<tr>
					<td><b>Segment :</b></td>
					<td>${segment}</td>
				</tr>
				<tr>
					<td><b>Product :</b></td>
					<td>${productName}</td>
				</tr>
				<tr>
					<td><b>Product Version :</b></td>
					<td>${productVer}</td>
				</tr>
				<tr>
					<td><b>Environment:</b></td>
					<td>${environment}</td>
				</tr>
				<tr>
					<td><b>Sub-Environment :</b></td>
					<td>${subEnvironment}</td>
				</tr>
				<tr>
					<td><b>Domain Service Account :</b></td>
					<td>${domainServiceAccount}</td>
				</tr>
				<tr>
					<td><b>Database :</b></td>
					<td>${dbServerHost}</td>
				</tr>
				<tr>
					<td><b>Admin Server :</b></td>
					<td>${adminServerHostName}</td>
				</tr>
				<tr>
					<td><b>Domain Admin Console URL :</b></td>
					<td><a
						href="http://${adminServerHostName}:${adminServerHttpPort}/console">http://${adminServerHostName}:${adminServerHttpPort}/console</a></td>
				</tr>
				<tr>
					<td><b>Domain SB/EM Console URL :</b></td>
					<td><a
						href="http://${adminServerHostName}:${adminServerHttpPort}/sbconsole">http://${adminServerHostName}:${adminServerHttpPort}/sbconsole</a></td>
				</tr>
			</table>
		</div>
		<br>
		<br> Thanks,<br /> EIS Team
	</div>
</body>

</html>