# Test configuration for template security
spring:
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false
  security:
    oauth2:
      client:
        registration:
          test-client:
            client-id: test-client-id
            client-secret: test-client-secret
            authorization-grant-type: client_credentials
            scope: read,write
        provider:
          test-provider:
            token-uri: http://localhost:8080/oauth/token

# Redis service configuration for tests
redis:
  service:
    url: http://localhost:8080

# Email configuration for tests
email:
  smtp:
    host: localhost
    username: test
    password: test
    port: 587

# API configuration for tests
api:
  user:
    name: test
    password: test

# Mail service configuration for tests
mail:
  service:
    core:
      pool:
        size: 2
    max:
      pool:
        size: 4
    queue:
      capacity: 10

template:
  security:
    # Test with smaller limits for faster testing
    max-size: 10000
    max-nesting-depth: 5
    processing-timeout: 2000
    max-output-size: 50000
    strict-validation: true
    log-security-violations: true

# Test logging configuration
logging:
  level:
    com.dell.it.eis.email.security: DEBUG
    root: INFO
