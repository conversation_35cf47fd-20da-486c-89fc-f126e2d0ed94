package com.dell.it.eis.email.security;

import java.io.StringWriter;
import java.util.Map;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dell.it.eis.email.config.TemplateSecurityProperties;
import com.dell.it.eis.email.exception.TemplateSecurityException;

import lombok.extern.slf4j.Slf4j;

/**
 * Secure template processing service that validates templates before processing
 */
@Service
@Slf4j
public class SecureTemplateService {

    @Autowired
    private TemplateSecurityValidator templateValidator;

    @Autowired
    private VelocityEngine velocityEngine;

    @Autowired
    private TemplateSecurityProperties securityProperties;

    /**
     * Securely processes a Velocity template with validation
     * 
     * @param template The template content from Redis
     * @param model The context variables for template processing
     * @return Processed template content
     * @throws TemplateSecurityException if template validation fails
     */
    public String processTemplateSecurely(String template, Map<String, Object> model) {
        log.debug("Processing template securely");
        
        try {
            // Step 1: Validate template for security issues
            templateValidator.validateTemplate(template);
            
            // Step 2: Create restricted Velocity context
            VelocityContext velocityContext = createRestrictedContext(model);
            
            // Step 3: Process template with security controls
            StringWriter result = new StringWriter();
            
            // Use a timeout to prevent infinite loops
            long startTime = System.currentTimeMillis();
            boolean success = velocityEngine.evaluate(velocityContext, result, "SecureTemplate", template);
            long processingTime = System.currentTimeMillis() - startTime;

            if (!success) {
                log.error("Template processing failed");
                throw new TemplateSecurityException("Template processing failed - invalid template syntax");
            }

            // Check for excessive processing time (potential DoS)
            long maxProcessingTime = securityProperties.getProcessingTimeoutSafe();
            if (processingTime > maxProcessingTime) {
                if (securityProperties.getLogSecurityViolations()) {
                    log.warn("Template processing took too long: {}ms > {}ms", processingTime, maxProcessingTime);
                }
                throw new TemplateSecurityException("Template processing timeout - potential DoS attack");
            }
            
            String processedContent = result.toString();

            // Step 4: Validate output size
            int maxOutputSize = securityProperties.getMaxOutputSizeSafe();
            if (processedContent.length() > maxOutputSize) {
                if (securityProperties.getLogSecurityViolations()) {
                    log.warn("Processed template output too large: {} characters > {}", processedContent.length(), maxOutputSize);
                }
                throw new TemplateSecurityException("Processed template output exceeds maximum size");
            }
            
            log.debug("Template processed successfully in {}ms", processingTime);
            return processedContent;
            
        } catch (TemplateSecurityException e) {
            log.error("Template security validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during template processing: {}", e.getMessage(), e);
            throw new TemplateSecurityException("Template processing failed due to unexpected error", e);
        }
    }

    /**
     * Creates a restricted Velocity context that limits access to dangerous objects
     */
    private VelocityContext createRestrictedContext(Map<String, Object> model) {
        VelocityContext context = new VelocityContext();
        
        if (model != null) {
            for (Map.Entry<String, Object> entry : model.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                // Validate context key
                if (!isValidContextKey(key)) {
                    if (securityProperties.getLogSecurityViolations()) {
                        log.warn("Skipping invalid context key: {}", key);
                    }
                    continue;
                }

                // Validate context value
                if (!isValidContextValue(value)) {
                    if (securityProperties.getLogSecurityViolations()) {
                        log.warn("Skipping potentially dangerous context value for key: {}", key);
                    }
                    continue;
                }
                
                context.put(key, value);
            }
        }
        
        return context;
    }

    /**
     * Validates that a context key is safe to use
     */
    private boolean isValidContextKey(String key) {
        if (key == null || key.trim().isEmpty()) {
            return false;
        }
        
        // Only allow alphanumeric keys with underscores
        return key.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }

    /**
     * Validates that a context value is safe to use in templates
     */
    private boolean isValidContextValue(Object value) {
        if (value == null) {
            return true; // null values are safe
        }
        
        // Allow basic types
        if (value instanceof String || 
            value instanceof Number || 
            value instanceof Boolean ||
            value instanceof java.util.Date) {
            return true;
        }
        
        // Allow collections of basic types
        if (value instanceof java.util.Collection || value instanceof java.util.Map) {
            return true; // Note: We could add deeper validation here if needed
        }
        
        // Block dangerous classes
        String className = value.getClass().getName();
        if (className.startsWith("java.lang.Class") ||
            className.startsWith("java.lang.Runtime") ||
            className.startsWith("java.lang.System") ||
            className.startsWith("java.lang.ProcessBuilder") ||
            className.startsWith("java.lang.reflect") ||
            className.startsWith("java.io") ||
            className.startsWith("java.nio") ||
            className.startsWith("java.net") ||
            className.startsWith("javax.script")) {
            return false;
        }
        
        return true;
    }
}
