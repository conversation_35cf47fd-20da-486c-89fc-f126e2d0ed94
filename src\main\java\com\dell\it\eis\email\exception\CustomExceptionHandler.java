package com.dell.it.eis.email.exception;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.dell.it.eis.email.domain.ApiResponse;
import com.dell.it.eis.email.util.Constants;

@ControllerAdvice
@RestController
public class CustomExceptionHandler extends ResponseEntityExceptionHandler {

  @ExceptionHandler(Exception.class)
  public final ResponseEntity<ApiResponse> handleAllException(Exception exception, WebRequest request) {
	SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.DATE_FORMAT);
	ApiResponse apiResponse = new ApiResponse(dateFormat.format(new Date()), exception.getMessage(),
        request.getDescription(false));
    return new ResponseEntity<>(apiResponse, HttpStatus.INTERNAL_SERVER_ERROR);
  }
  
  @ExceptionHandler(TemplateNotFoundException.class)
  public final ResponseEntity<ApiResponse> handleTemplateNotFoundException(TemplateNotFoundException ex, WebRequest request) {
	  SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.DATE_FORMAT);
	  ApiResponse apiResponse = new ApiResponse(dateFormat.format(new Date()), ex.getMessage(),
        request.getDescription(false));
    return new ResponseEntity<>(apiResponse, HttpStatus.NOT_FOUND);
  }
  
  
  @ExceptionHandler(BadRequestException.class)
  public final ResponseEntity<ApiResponse> handleBadRequestException(BadRequestException ex, WebRequest request) {
	  SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.DATE_FORMAT);
	  ApiResponse apiResponse = new ApiResponse(dateFormat.format(new Date()), ex.getMessage(),
        request.getDescription(false));
    return new ResponseEntity<>(apiResponse, HttpStatus.BAD_REQUEST);
  }
  
  
  
  
}
