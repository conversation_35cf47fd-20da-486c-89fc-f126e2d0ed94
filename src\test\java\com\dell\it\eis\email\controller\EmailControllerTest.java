package com.dell.it.eis.email.controller;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.request;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.io.IOException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import com.dell.it.eis.email.domain.ApiResponse;
import com.dell.it.eis.email.domain.GenericEmailDto;
import com.dell.it.eis.email.exception.BadRequestException;
import com.dell.it.eis.email.exception.CannotSendEmailException;
import com.dell.it.eis.email.service.EmailService;
import com.dell.it.eis.email.util.EmailUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.servlet.http.HttpServletRequest;

class EmailControllerTest {

	@Mock
	private MockHttpServletRequest request1;

	private MockMvc mockMvc;
	
	EmailController emailController;
	EmailService emailService;
	EmailUtil emailUtil;
	GenericEmailDto emailControllerGenericEmailDto = new GenericEmailDto();
	private final String emailControllerGenericEmailAsynDto = null;
	private final String toAddress = "<EMAIL>";
	String emailControllerJson;
	HttpServletRequest request;
	CannotSendEmailException excep;

	@BeforeEach
	void setUp() {
		emailController = new EmailController();
		emailService = Mockito.mock(EmailService.class);
		emailUtil = Mockito.mock(EmailUtil.class);
		emailControllerGenericEmailDto.setToAddress(toAddress);
		emailControllerJson = new Gson().toJson(emailControllerGenericEmailDto);
		mockMvc = MockMvcBuilders.standaloneSetup(emailController).build();

		ReflectionTestUtils.setField(emailController, "emailService", emailService);
		ReflectionTestUtils.setField(emailController, "emailUtil", emailUtil);
	}

	@Test
	void send_generic_email() throws AddressException, MessagingException {
		doNothing().when(emailService).sendGenericEmail(Mockito.any(), Mockito.any(), Mockito.any());
		doNothing().when(emailUtil).validateRequest(any(), any());
		ResponseEntity<ApiResponse> response = emailController.sendGenericEmail(emailControllerGenericEmailDto,
				Mockito.any(), Mockito.any());
		Assertions.assertNotNull(response);
	}

	@Test
	void send_generic_email_asyn() throws AddressException, MessagingException {
		doNothing().when(emailService).sendGenericEmail(Mockito.any(), Mockito.any(), Mockito.any());
		doNothing().when(emailUtil).validateRequest(any(), any());
		ResponseEntity<ApiResponse> response = emailController.sendGenericEmailAsyn(emailControllerJson, Mockito.any(),
				Mockito.any(), request);
		Assertions.assertNotNull(response);
	}

	@Test
	void sendEmailBadRequest() throws Exception {

		GenericEmailDto dto = new GenericEmailDto();
		dto.setSubject("subject");
		dto.setBody("body");
		dto.setFromAddress("<EMAIL>");
		dto.setSubject("subject");
		dto.setToAddress("<EMAIL>");
		String templateName = "glovia";

		dto.setTemplateMap(null);

		MultipartFile file = null;

		ObjectMapper mapper = new ObjectMapper();
		String requestBody = mapper.writeValueAsString(dto);

		// Mock Service calls
		doNothing().when(emailService).sendGenericEmail(dto, templateName, file);
		this.mockMvc
		.perform(post("/api/v1/generic/email").param("templateName", templateName)
				.contentType(MediaType.APPLICATION_JSON).content(requestBody)
				.contentType(MediaType.MULTIPART_FORM_DATA_VALUE).content("file".getBytes()))
		.andExpect(status().isBadRequest()).andDo(print());
	}

	@Test
	void testsendGenericEmailGivenException() throws AddressException, MessagingException {

		String emailControllerTemplateName = null;
		doNothing().when(emailUtil).validateRequest(emailControllerGenericEmailDto, emailControllerTemplateName);
		doThrow(new CannotSendEmailException()).when(emailService).sendGenericEmail(Mockito.any(), Mockito.any(), Mockito.any());
		try {
			emailController.sendGenericEmail(emailControllerGenericEmailDto, null, emailControllerTemplateName);
		} catch (Exception e) { }
	}


	@Test
	void testsendGenericEmailAsynGivenBadRequestException() throws AddressException, MessagingException, IOException {
		assertThrows(BadRequestException.class, () -> {

			String emailControllerTemplateNameString = null;
			doNothing().when(emailUtil).validateRequest(emailControllerGenericEmailDto, emailControllerTemplateNameString);
			when(emailController.sendGenericEmail(emailControllerGenericEmailDto, null, emailControllerTemplateNameString)).thenThrow(BadRequestException.class);
			emailController.sendGenericEmailAsyn(emailControllerGenericEmailAsynDto, null, emailControllerTemplateNameString, request);
		});
	}

	@Test
	void testsendGenericEmailAsynGivenCannotSendEmailException() throws AddressException, MessagingException, JsonProcessingException {
		emailController.getEmail();
		String emailControllerTemplateNameString = null;

		GenericEmailDto dto = new GenericEmailDto();
		dto.setFromAddress("test");
		String dtoString = new ObjectMapper().writeValueAsString(dto);

		doNothing().when(emailUtil).validateRequest(emailControllerGenericEmailDto, emailControllerTemplateNameString);
		doThrow(new CannotSendEmailException()).when(emailService).sendGenericEmail(Mockito.any(), Mockito.any(), Mockito.any());
		try {
			emailController.sendGenericEmailAsyn(dtoString, null, emailControllerTemplateNameString, request);
		} catch (Exception e) { }
	}
}
