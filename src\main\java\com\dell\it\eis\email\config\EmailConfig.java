package com.dell.it.eis.email.config;

import java.util.Properties;
import java.util.concurrent.Executor;

import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.dell.it.eis.email.util.Constants;

import jakarta.mail.Authenticator;
import jakarta.mail.PasswordAuthentication;
import jakarta.mail.Session;

@Configuration
@EnableAsync
public class EmailConfig {

	@Value("${email.smtp.host}")
	private String mailHost;

	@Value("${email.smtp.username}")
	private String mailSmtpUsername;

	@Value("${email.smtp.password}")
	private String mailSmtpPassword;

	@Value("${email.smtp.port}")
	private Integer mailPort;

	@Value("${email.smtp.protocols:TLSv1.2}")
	private String mailProtocols;

	@Value("${email.smtp.starttls.enable:true}")
	private String mailStartTlsEnable;

	@Value("${email.smtp.auth:true}")
	private String mailAuth;

	@Value("${mail.service.core.pool.size}")
	private Integer execCorePoolSize;

	@Value("${mail.service.max.pool.size}")
	private Integer execMaxPoolSize;

	@Value("${mail.service.queue.capacity}")
	private Integer execQueueCapacity;

	
	@Bean
	JavaMailSender javaMailSender() {
		JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
		javaMailSender.setDefaultEncoding(Constants.UTF_8);
		Properties properties = getMailProperties();
		javaMailSender.setJavaMailProperties(properties);
		javaMailSender.setSession(buildSession(properties));
		return javaMailSender;
	}
	
	@Primary
	@Bean(name = "emailAsyncExecutor")
	Executor asyncExecutor() 
	{
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(execCorePoolSize);
		executor.setMaxPoolSize(execMaxPoolSize);
		executor.setQueueCapacity(execQueueCapacity);
		executor.setThreadNamePrefix("EmailAsyncThread-");
		executor.initialize();
		return executor;
	}
	

	private Session buildSession(Properties properties) {
		return Session.getDefaultInstance(properties, new Authenticator() {

			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(mailSmtpUsername, mailSmtpPassword);
			}
		}

		);
	}

	private Properties getMailProperties() {
		Properties properties = new Properties();
		properties.put("mail.smtp.host", mailHost);
		properties.put("mail.smtp.port", mailPort);
		properties.put("mail.smtp.starttls.enable", mailStartTlsEnable);
		properties.put("mail.smtp.auth", mailAuth);
		properties.put("mail.smtp.ssl.protocols", mailProtocols);
		properties.put("mail.smtp.ssl.trust", mailHost);
		return properties;
	}


	@Bean
	VelocityEngine velocityEngine() {
	    Properties properties = new Properties();
	    properties.setProperty("input.encoding", Constants.UTF_8);
	    properties.setProperty("output.encoding", Constants.UTF_8);
	    properties.setProperty("resource.loader", "class");	    
	   
	    properties.setProperty("class.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
	    return new VelocityEngine(properties);	  	   
	}
	
}
